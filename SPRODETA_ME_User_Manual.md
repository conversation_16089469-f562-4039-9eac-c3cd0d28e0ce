# SPRODETA M&E System User Manual

**Version:** 1.0
**Last Updated:** January 2025
**System Version:** SPRODETA M&E v2.0

---

## Introduction

Welcome to the SPRODETA M&E (Monitoring & Evaluation) System User Manual. This comprehensive guide is designed to help you effectively use the SPRODETA project management platform for development organizations.

### Purpose of This Manual
This manual serves as your complete reference guide for:
- Understanding system capabilities and features
- Learning role-specific workflows and processes
- Troubleshooting common issues
- Maximizing productivity within the system
- Ensuring proper project management practices

### Purpose of the SPRODETA M&E System
The SPRODETA M&E system is specifically designed for development organizations to:
- **Streamline Project Management**: From conception to completion with integrated planning tools
- **Enhance Monitoring & Evaluation**: Real-time tracking of project progress and impact
- **Improve Financial Oversight**: Comprehensive budget management and financial reporting
- **Facilitate Team Collaboration**: Role-based access and communication tools
- **Ensure Accountability**: Detailed reporting and audit trails
- **Support Decision Making**: Data-driven insights and analytics

### Who Should Use This Manual
- **New Users**: Complete onboarding and system familiarization
- **Existing Users**: Reference for advanced features and troubleshooting
- **System Administrators**: Comprehensive system management guidance
- **Training Coordinators**: Resource for user training programs
- **Management**: Understanding system capabilities and reporting features

### How to Use This Manual
- **Sequential Reading**: For new users, read sections 1-3 first
- **Role-Specific Sections**: Jump directly to your role's guide (sections 4-8)
- **Quick Reference**: Use the table of contents for specific topics
- **Troubleshooting**: Refer to FAQ and troubleshooting sections as needed

---

## Table of Contents
1. [Introduction](#introduction)
2. [System Compatibility & Requirements](#system-compatibility--requirements)
3. [Login & Access Instructions](#login--access-instructions)
4. [System Overview](#system-overview)
5. [User Roles and Permissions](#user-roles-and-permissions)
6. [Senior Manager Guide](#senior-manager-guide)
7. [Project Manager Guide](#project-manager-guide)
8. [Field Officer Guide](#field-officer-guide)
9. [Accountant Guide](#accountant-guide)
10. [Admin Guide & Tools](#admin-guide--tools)
11. [Common Features](#common-features)
12. [Frequently Asked Questions (FAQ)](#frequently-asked-questions-faq)
13. [Troubleshooting](#troubleshooting)
14. [Version Information](#version-information)

---

## System Compatibility & Requirements

### Supported Browsers
**Recommended Browsers (Latest Versions):**
- **Google Chrome** 90+ (Recommended)
- **Mozilla Firefox** 88+
- **Microsoft Edge** 90+
- **Safari** 14+ (macOS/iOS)

**Browser Requirements:**
- JavaScript enabled
- Cookies enabled
- Local storage support
- Minimum screen resolution: 1024x768

### Operating System Compatibility
**Desktop/Laptop:**
- Windows 10/11
- macOS 10.15+
- Linux (Ubuntu 18.04+, CentOS 7+)

**Mobile Devices:**
- iOS 13+ (Safari, Chrome)
- Android 8+ (Chrome, Firefox)

### Network Requirements
- **Internet Connection**: Broadband recommended (minimum 1 Mbps)
- **Ports**: Standard HTTP (80) and HTTPS (443)
- **Firewall**: Allow access to system domain
- **Bandwidth**: 2-5 MB for PDF downloads and file uploads

### Hardware Requirements
**Minimum:**
- 4GB RAM
- 1GB available storage
- Dual-core processor

**Recommended:**
- 8GB+ RAM
- 2GB+ available storage
- Quad-core processor
- Dedicated graphics (for better chart rendering)

### File Upload Specifications
- **Maximum File Size**: 10MB per file
- **Supported Formats**: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG
- **Total Attachments**: Up to 5 files per report
- **Document Requirements**: Project approval documents must be PDF format

---

## Login & Access Instructions

### Getting Your Account
**For New Users:**
1. **Account Creation**: Contact your system administrator
2. **Required Information**: Full name, email, phone number, district, role
3. **Account Activation**: You'll receive login credentials via email
4. **Initial Password**: Change on first login (recommended)

### First-Time Login Process
1. **Access the System**:
   - Open your web browser
   - Navigate to: `[SYSTEM_URL]` (provided by administrator)
   - Bookmark the page for easy access

2. **Login Steps**:
   - Enter your email address
   - Enter your temporary password
   - Click "Login"
   - You'll be prompted to change your password

3. **Password Requirements**:
   - Minimum 8 characters
   - Include uppercase and lowercase letters
   - Include at least one number
   - Include at least one special character

4. **Initial Setup**:
   - Complete your profile information
   - Upload a profile picture (optional)
   - Set notification preferences
   - Review your role permissions

### Regular Login Process
1. Navigate to the system URL
2. Enter your email and password
3. Click "Login"
4. You'll be redirected to your role-specific dashboard

### Account Security
**Best Practices:**
- Use a strong, unique password
- Don't share your login credentials
- Log out when finished, especially on shared computers
- Report suspicious activity to administrators
- Update your password regularly (every 90 days recommended)

**Session Management:**
- Sessions expire after 30 days of inactivity
- You'll be automatically logged out for security
- Save your work frequently
- Use "Remember Me" only on personal devices

### Forgot Password
1. Click "Forgot Password" on login page
2. Enter your email address
3. Check your email for reset instructions
4. Follow the link to create a new password
5. Return to login page with new credentials

### Access Issues
**If you cannot log in:**
- Verify your email and password
- Check for typos and correct capitalization
- Ensure your account is active
- Contact your system administrator
- Check if your browser meets requirements

**Account Lockout:**
- Accounts lock after 5 failed login attempts
- Wait 15 minutes or contact administrator
- Ensure you're using the correct credentials

---

## System Overview

SPRODETA M&E (Monitoring & Evaluation) is a comprehensive project management system designed for development organizations. The system facilitates project creation, planning, implementation, monitoring, and evaluation through a role-based interface.

### Key Features
- **Project Management**: Complete project lifecycle management from creation to completion
- **Activity Management**: Detailed activity planning, assignment, and tracking
- **Budget Management**: Financial planning, monitoring, and reporting
- **Team Management**: Team creation and assignment to projects
- **Reporting System**: Comprehensive reporting and analytics
- **M&E Framework**: Built-in monitoring and evaluation tools
- **Real-time Notifications**: In-app and email notifications
- **Dashboard Analytics**: Role-specific dashboards with visual analytics

### System Architecture
- **Frontend**: Next.js web application with responsive design
- **Backend**: Node.js/Express API with MongoDB database
- **Authentication**: JWT-based authentication with role-based access control
- **Currency**: All financial data displayed in MWK (Malawian Kwacha)

---

## Getting Started

### System Requirements
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection
- Valid user credentials

### Accessing the System
1. Navigate to the SPRODETA M&E system URL
2. Enter your email and password on the login page
3. Click "Login" to access your role-specific dashboard

### First Login
- You will be redirected to your role-specific dashboard
- Update your profile information if needed
- Familiarize yourself with the navigation menu
- Check notification settings

---

## User Roles and Permissions

The system has five distinct user roles with specific permissions:

### 1. Senior Manager
- **Primary Functions**: Project creation, strategic oversight, comprehensive reporting
- **Access Level**: System-wide view of all projects and data
- **Key Responsibilities**: 
  - Create and approve projects
  - Assign projects to project managers
  - Monitor overall project portfolio
  - Generate comprehensive reports

### 2. Project Manager
- **Primary Functions**: Project implementation, activity management, team coordination
- **Access Level**: Projects assigned to them and related data
- **Key Responsibilities**:
  - Manage assigned projects
  - Create and assign activities to field officers
  - Approve activity reports
  - Monitor project progress and budgets

### 3. Field Officer
- **Primary Functions**: Activity execution, report submission
- **Access Level**: Activities assigned to them
- **Key Responsibilities**:
  - Execute assigned activities
  - Submit activity reports
  - Track activity progress
  - Communicate with project managers

### 4. Accountant
- **Primary Functions**: Budget monitoring, financial oversight
- **Access Level**: Financial data across all projects
- **Key Responsibilities**:
  - Monitor budget utilization
  - Generate financial reports
  - Track project expenditures
  - Ensure financial compliance

### 5. Admin
- **Primary Functions**: System administration, user management
- **Access Level**: Full system access
- **Key Responsibilities**:
  - Manage user accounts
  - Create and assign teams
  - Monitor system activity
  - Maintain system settings

---

## Senior Manager Guide

### Dashboard Overview
The Senior Manager dashboard provides a comprehensive view of the entire project portfolio:

#### Key Metrics Cards
- **Total Projects**: Shows active projects (approved/awaiting status)
- **In Progress**: Displays approved projects currently being implemented
- **Completed**: Shows projects where all activities are reported and completed
- **Team Members**: Total count of all project managers in the system

#### Visual Analytics
- **Project Progress Charts**: Interactive graphs showing completion percentages based on activities
- **Budget Distribution**: Pie charts showing budget allocation across projects
- **Recent Activities**: Latest project and activity updates

### Project Management

#### Creating a New Project
1. Navigate to **Projects** → **Create Project**
2. Complete the 4-step project creation process:

**Step 1: Basic Information**
- Project Title
- Project Description

**Step 2: Project Details**
- Objectives (link to project objectives collection)
- Goals (stored in project goals collection)
- Beneficiaries information

**Step 3: Location and Timeline**
- Select district from database dropdown
- Set start and end dates
- Choose project category

**Step 4: Budget and Documents**
- Set total project budget
- Upload approval documents (mandatory)

3. **Project Planning Phase**:
   - Add project activities (stored in project activities collection)
   - Define deliverables and milestones
   - Set project resources and risk assessments

4. **Manager Assignment**:
   - Select project manager from available list
   - System sends notification to assigned manager
   - Project status automatically set to "approved"

#### Project Monitoring
- **Projects Tab**: View all projects with title, description, status, and location
- **Project Progress**: Use dropdown to select projects and view:
  - Project Activities tab
  - Project Milestones tab  
  - Project Deliverables tab
  - Project Timeline (Gantt Chart) tab

### Reports and Analytics

#### Reports Page Features
- **Project Selection Dropdown**: Choose individual project or "All Projects"
- **Data Cards Display**:
  - Project Progress metrics
  - Budget Utilization statistics
  - Budget Distribution analytics
  - Project Status Distribution

#### When "All Projects" Selected:
- Projects table is hidden
- Only PDF export button is shown
- Export includes overall analytics with graphs and charts

#### Individual Project Reports:
- Detailed project information table
- Per-project PDF export buttons
- Activity KPI Performance graphs (target vs actual outcomes)

#### PDF Export Features:
- Project details and project plan
- Project progress graphs
- Budget utilization charts
- Budget distribution visualizations
- Full currency amounts (not abbreviated)
- MWK currency symbol throughout

### Notifications
Senior Managers receive notifications for:
- Schedule slippage alerts
- Budget issues and overruns
- Manager report submissions
- Project completion updates

---

## Project Manager Guide

### Dashboard Overview
The Project Manager dashboard focuses on assigned projects and activities:

#### Key Metrics
- **Total Activities**: All activities in assigned projects
- **In Progress**: Currently active activities
- **Completed**: Activities with approved reports
- **Assigned Projects**: Projects under management

#### Activity Progress Tracking
- **Target vs Actual Outcomes**: Visual comparison charts
- **KPI Performance**: Graphs showing performance against targets
- **Recent Activities**: Latest activity updates

### Project Management

#### Managing Assigned Projects
1. **View Projects**: Access via **Projects** menu
2. **Project Activities**: 
   - View activities created during project planning
   - Activities are stored in project activities collection
   - Assign activities to field officers from dashboard

#### Activity Assignment Process
1. **Select Activity**: From project activities list
2. **Choose Field Officer**: From project-specific teams only
3. **Fill Assignment Details**:
   - Start and end dates
   - Budget allocation
   - Location specifics
   - Target outcomes
   - Priority level
   - KPI metrics for evaluation

4. **Submit Assignment**: 
   - Activity status changes to "pending"
   - Field officer receives notification
   - Activity becomes active for execution

### Team Management

#### Creating Project Teams
1. **Access Teams**: Via **Projects** dropdown → **Teams**
2. **Team Creation Process**:
   - Select project first
   - Choose field officers from same location as project
   - Create project-specific teams
   - Teams are linked to specific projects

#### Team Assignment
- Teams are automatically available for activity assignment
- Only officers from project teams can be assigned activities
- Team members receive notifications when assigned

### Report Management

#### Approving Activity Reports
1. **Access Reports**: Via **Reports** menu or dashboard
2. **Review Submissions**:
   - View report details and attachments
   - Check actual outcomes against targets
   - Verify budget expenditure

3. **Approval Process**:
   - Approve: Activity marked as complete, budget updated
   - Reject: Return to field officer with feedback
   - Activities only complete when reports are approved

#### Budget Impact
- Approved reports automatically update:
  - Project remaining budget
  - Project progress percentage
  - Activity completion status

### M&E (Monitoring & Evaluation)

#### Performance Tracking
- **Ongoing Evaluation**: Monitor current project performance
- **Post-Project Evaluation**: Assess completed projects
- **KPI Performance**: Visual charts showing target vs actual outcomes
- **Project Filtering**: View M&E data for specific projects only

#### Evaluation Metrics
- Effectiveness, Efficiency, Impact, Sustainability, Relevance
- Based on actual KPI performance from approved activities
- Visual charts and exportable reports

### Budget Management

#### Budget Overview
- **Project Selection Dropdown**: Choose specific project or all projects
- **All Projects View**: Overview of all project budgets with charts and graphs
- **Individual Project View**: 
  - Activity budget breakdown
  - Detailed budget tables
  - Comprehensive budget management tools

#### Budget Calculation
- Uses approved activity reports with amountSpent values
- Real-time budget utilization tracking
- Automatic updates when reports are approved

### Notifications
Project Managers receive notifications for:
- Project assignment
- Activity report submissions
- Late report submissions
- Team assignments
- Budget alerts

---

## Field Officer Guide

### Dashboard Overview
The Field Officer dashboard focuses on assigned activities and reporting:

#### Key Metrics Cards (Clickable for Navigation)
- **Assigned Activities**: Total activities assigned → Links to Activities page
- **Active Activities**: Currently in progress → Links to Activities page  
- **Pending Reports**: Reports awaiting approval → Links to Reports page
- **My Completed Activities**: Number of completed activities (not percentage)

### Activity Management

#### Viewing Assigned Activities
1. **Access Activities**: Via **Activities** menu or dashboard cards
2. **Activity Details**: View all assigned activity information including:
   - Project context
   - Timeline and deadlines
   - Budget allocation
   - Target outcomes
   - KPI requirements

#### Activity Execution
- Track progress against assigned activities
- Monitor deadlines and deliverables
- Coordinate with project managers as needed
- Prepare for report submission

### Report Submission

#### Customizable Report Form
1. **Access Submit Report**: Via **Submit Report** menu
2. **Dynamic Form Builder**:
   - Add/modify form components as needed
   - Customize fields based on activity requirements
   - Single form interface for all submissions

3. **Report Structure**:
   - Activity selection and details
   - Actual outcomes achieved
   - Budget expenditure (amountSpent field)
   - File attachments
   - Report submission details (positioned below attachments)

#### Submission Process
- **Submit to Project Manager**: Reports automatically routed to activity assignor
- **Notifications Sent**: Email and in-app notifications to project manager
- **Status Tracking**: Monitor approval status in My Reports

### My Reports

#### Report Management
1. **Access My Reports**: Via **My Reports** menu or dashboard
2. **View All Reports**: Both approved and unapproved reports displayed
3. **Report Status**: Track approval progress
4. **Report History**: Complete submission history

### Notifications
Field Officers receive notifications for:
- Activity assignments
- Report approval/rejection
- Deadline reminders
- Overdue report alerts

---

## Accountant Guide

### Dashboard Overview
The Accountant dashboard provides financial oversight across all projects:

#### Key Metrics Cards (Clickable)
- **Total Budget**: All requested budgets → Links to Budgets page
- **Utilization**: Budget usage rate percentage
- **Total Projects**: Count of all active projects

### Budget Management

#### Budget Monitoring
1. **Access Budgets**: Via **Budgets** menu or dashboard
2. **Project Selection**: Choose specific project or view all projects
3. **Financial Analytics**:
   - Budget utilization graphs and charts
   - Project-wise budget breakdown
   - Spending patterns and trends

#### Budget Calculation Method
- **Real-time Calculation**: Based on approved activity reports
- **Data Source**: amountSpent values from approved reports
- **Automatic Updates**: Budget utilization updates when reports are approved

#### PDF Export Features
- **Project-Specific Exports**: Select project for targeted financial reports
- **Comprehensive Reports**: Include financial data and visualizations
- **Chart Integration**: Graphs and charts included in exports

### Financial Reporting

#### Report Generation
- **Budget Distribution**: How funds are allocated across activities
- **Utilization Analysis**: Actual spending vs planned budget
- **Project Performance**: Financial performance metrics
- **Trend Analysis**: Spending patterns over time

### Notifications
Accountants receive notifications for:
- Project creation (with budget details)
- Activity creation (with budget information)
- Project completion events
- Budget overuse alerts
- Budget underuse alerts

---

## Admin Guide & Tools

### Dashboard Overview
The Admin dashboard provides system-wide management capabilities and comprehensive administrative tools:

#### Key Metrics
- **Total Users**: All system users across all roles
- **Active Users**: Currently logged in users (real-time status)
- **New Users Today**: Recent registrations and account activations
- **Total Roles**: Available system roles and permissions

#### User Status Tracking
- **Online Status**: Shows active (logged in) or offline (logged out)
- **Real-time Updates**: Status updates every 30 seconds automatically
- **Last Seen**: Timestamp of last user activity
- **Session Management**: Monitor active sessions and timeouts

### User Management Tools

#### Creating Users
1. **Access Users**: Via **Users** menu in admin panel
2. **Add New User Process**:
   - Full name and contact information
   - Email address (must be unique)
   - Temporary password generation
   - Role assignment (from available roles)
   - District assignment (from database dropdown)
   - Account activation settings

#### Advanced User Administration
- **View All Users**: Complete user directory with filtering
- **Edit User Details**: Update user information and settings
- **Role Management**: Change user roles and permissions
- **Account Status**: Enable/disable accounts and manage access
- **Password Reset**: Force password resets for security
- **Bulk Operations**: Import/export user data
- **User Activity**: Monitor user login patterns and system usage

#### User Role Management
**Available Roles:**
- Senior Manager
- Project Manager
- Field Officer
- Accountant
- Admin

**Role Permissions Matrix:**
- **Senior Manager**: Project creation, system-wide reporting, manager assignment
- **Project Manager**: Activity management, team creation, report approval
- **Field Officer**: Activity execution, report submission
- **Accountant**: Budget monitoring, financial reporting
- **Admin**: Full system access, user management, system configuration

### Team Management Tools

#### Creating and Managing Teams
1. **Access Teams**: Via navigation menu in admin panel
2. **Team Creation Process**:
   - Team name and description
   - Select team members from user database
   - Assign team location/district
   - Set team leader/coordinator
   - Define team objectives and scope

#### Advanced Team Administration
- **Team Assignment**: Link teams to specific project managers
- **Team Details**: Clickable team cards show detailed member information
- **Team Management**: Update team composition and assignments
- **Team Performance**: Monitor team activity and project assignments
- **Cross-Project Teams**: Manage teams working on multiple projects

### System Monitoring & Activity Logs

#### Comprehensive System Monitoring
1. **Access Activity Logs**: Via **Activity Logs** menu
2. **Log Categories**:
   - User login/logout tracking
   - System activity monitoring
   - Error and exception logging
   - Security events and access attempts
   - Data modification tracking
   - Export and import activities

#### Advanced Log Management
- **Filter Options**: By date range, user, activity type, severity level
- **Export Functions**: Download logs in CSV, PDF, or Excel format
- **Cleanup Tools**: Automated removal of old log entries
- **Real-time Monitoring**: Live activity feed for system events
- **Alert Configuration**: Set up automated alerts for critical events

#### Security Monitoring
- **Failed Login Attempts**: Track and investigate security breaches
- **Unusual Activity**: Monitor for suspicious user behavior
- **Access Patterns**: Analyze user access patterns and usage
- **Data Integrity**: Monitor for unauthorized data changes

### System Configuration Tools

#### Location and District Management
- **District Database**: Add, edit, and maintain district information
- **Location Hierarchy**: Manage regional and district relationships
- **Location Fields**: Ensure proper district display (not IDs)
- **Data Integrity**: Maintain consistent location data across system
- **Geographic Mapping**: Link locations to projects and users

#### System Settings Management
- **Global Settings**: Configure system-wide parameters
- **Notification Settings**: Manage email and in-app notification templates
- **Security Settings**: Configure password policies and session timeouts
- **File Upload Settings**: Manage file size limits and allowed formats
- **Currency Settings**: Configure MWK display and formatting

#### Database Management Tools
- **Data Backup**: Schedule and manage system backups
- **Data Migration**: Tools for system updates and data transfers
- **Performance Monitoring**: Database performance and optimization
- **Data Cleanup**: Remove obsolete or test data
- **Index Management**: Optimize database queries and performance

### Advanced Administrative Features

#### System Maintenance
- **Scheduled Maintenance**: Plan and execute system updates
- **Performance Optimization**: Monitor and improve system performance
- **Cache Management**: Clear and manage system caches
- **Error Handling**: Monitor and resolve system errors
- **Update Management**: Deploy system updates and patches

#### Reporting and Analytics
- **System Usage Reports**: Generate comprehensive usage statistics
- **Performance Reports**: System performance and response time analysis
- **User Activity Reports**: Detailed user behavior and engagement metrics
- **Security Reports**: Security events and compliance reporting
- **Custom Reports**: Create specialized administrative reports

#### Integration Management
- **Email Integration**: Configure SMTP settings and email templates
- **External APIs**: Manage third-party integrations
- **Data Import/Export**: Bulk data operations and system integration
- **Backup Integration**: Configure automated backup systems

### Emergency Procedures

#### System Recovery
- **Backup Restoration**: Procedures for data recovery
- **Emergency Contacts**: Key personnel for system issues
- **Escalation Procedures**: When and how to escalate critical issues
- **Disaster Recovery**: Business continuity planning

#### Security Incidents
- **Incident Response**: Steps for handling security breaches
- **User Account Lockdown**: Emergency account suspension procedures
- **Data Protection**: Protecting sensitive information during incidents
- **Communication Protocols**: Notifying stakeholders of security issues

### Admin Best Practices

#### Daily Administrative Tasks
- Monitor system performance and user activity
- Review security logs for unusual activity
- Check backup completion and integrity
- Respond to user support requests
- Update system documentation

#### Weekly Administrative Tasks
- Generate system usage reports
- Review and clean up old log files
- Update user accounts and permissions
- Monitor storage usage and capacity
- Review system security settings

#### Monthly Administrative Tasks
- Comprehensive system backup verification
- User access review and cleanup
- Performance optimization and tuning
- Security audit and compliance check
- System update planning and deployment

### Notifications and Alerts
Admins receive comprehensive notifications for:
- System errors and critical issues
- Security events and failed login attempts
- User account creation and modification requests
- System performance alerts and warnings
- Backup completion and failure notifications
- Scheduled maintenance reminders

---

## Common Features

### Navigation Structure
All user interfaces include:
- **Collapsible Sidebar**: Main navigation menu
- **User Greeting**: Personalized welcome message in navbar
- **Notification Bell**: Real-time notifications beside profile icon
- **Profile Access**: User profile and settings
- **Search Functionality**: Context-sensitive search (where applicable)

### Notification System

#### In-App Notifications
- **Notification Bell**: Shows unread count
- **Notification Modal**: Click bell to view recent notifications
- **View All Button**: Navigate to full notifications page
- **Close Icon**: Dismiss notification modal

#### Email Notifications
- **Automatic Emails**: Sent for key system events
- **User Settings**: Control email notification preferences
- **Notification Types**: Project updates, activity assignments, report submissions

### User Interface Design

#### Design Principles
- **Professional Appearance**: Clean, modern interface
- **Baby Blue Theme**: White cards with baby blue borders
- **Responsive Design**: Works on desktop and mobile devices
- **User-Friendly**: Intuitive navigation and clear labeling

#### Common UI Elements
- **Dashboard Cards**: Hover effects and click navigation
- **Form Styling**: Labels above input fields
- **Button Design**: Consistent styling across all forms
- **Progress Indicators**: Visual progress bars and charts
- **Modal Windows**: Detailed information overlays

### Footer
- **Copyright Notice**: "© 2025 SPRODETA. All rights reserved."
- **Proper Alignment**: Aligns with sidebar without overlapping

---

## Frequently Asked Questions (FAQ)

### General System Questions

**Q: What is SPRODETA M&E and who should use it?**
A: SPRODETA M&E is a comprehensive project management system designed for development organizations. It's used by senior managers, project managers, field officers, accountants, and system administrators to manage projects from creation to completion.

**Q: How do I get access to the system?**
A: Contact your system administrator to create an account. You'll need to provide your full name, email, phone number, district, and desired role. You'll receive login credentials via email.

**Q: Can I use the system on my mobile device?**
A: Yes, the system is responsive and works on mobile devices with iOS 13+ or Android 8+. However, some features work better on desktop/laptop computers.

**Q: What browsers are supported?**
A: We recommend Google Chrome 90+, Mozilla Firefox 88+, Microsoft Edge 90+, or Safari 14+. Ensure JavaScript and cookies are enabled.

**Q: How often is my data backed up?**
A: The system performs automated backups daily. However, we recommend keeping local copies of important documents and reports.

### Account and Login Questions

**Q: I forgot my password. How do I reset it?**
A: Click "Forgot Password" on the login page, enter your email, and follow the instructions sent to your email. If you don't receive the email, check your spam folder or contact your administrator.

**Q: Why is my account locked?**
A: Accounts lock after 5 failed login attempts for security. Wait 15 minutes for automatic unlock or contact your administrator for immediate assistance.

**Q: How do I change my password?**
A: Go to your profile settings and select "Change Password." Enter your current password and new password twice to confirm.

**Q: Can I update my profile information?**
A: Yes, you can update most profile information in your user settings. Contact your administrator for role or district changes.

### Project Management Questions

**Q: Who can create projects?**
A: Only Senior Managers can create new projects. They also assign projects to Project Managers for implementation.

**Q: How are activities assigned to field officers?**
A: Project Managers assign activities to field officers from project-specific teams. Field officers must be part of a team linked to the project.

**Q: When is a project considered complete?**
A: Projects are automatically marked complete when they reach 100% progress or when all activities are completed and their reports are approved.

**Q: Can I modify a project after it's created?**
A: Project details can be modified by Senior Managers. Project Managers can update activity details and assignments within their assigned projects.

### Budget and Financial Questions

**Q: How is budget utilization calculated?**
A: Budget utilization is calculated based on the amountSpent values from approved activity reports, providing real-time budget tracking.

**Q: Who can approve budgets?**
A: Accountants monitor budgets, but Project Managers approve activity reports which automatically update budget utilization.

**Q: What currency does the system use?**
A: All financial data is displayed in MWK (Malawian Kwacha). The system shows full amounts, not abbreviated versions.

**Q: Can I export financial reports?**
A: Yes, Accountants and Senior Managers can export comprehensive financial reports in PDF format with charts and graphs.

### Reporting Questions

**Q: How do I submit an activity report?**
A: Field Officers use the "Submit Report" page with a customizable form. Add required information, attach files, and submit to your Project Manager.

**Q: When are reports considered approved?**
A: Reports are approved when Project Managers review and approve them. Only approved reports count toward project completion and budget utilization.

**Q: Can I edit a report after submission?**
A: No, reports cannot be edited after submission. If changes are needed, contact your Project Manager who may reject the report for resubmission.

**Q: What file formats can I attach to reports?**
A: Supported formats include PDF, DOC, DOCX, XLS, XLSX, JPG, and PNG. Maximum file size is 10MB per file, up to 5 files per report.

### Team and User Management Questions

**Q: How are teams created?**
A: Admins create general teams, while Project Managers create project-specific teams by selecting field officers from the same location as the project.

**Q: Can a user have multiple roles?**
A: No, each user has one primary role. Contact your administrator if you need role changes or additional access.

**Q: How do I know if someone is online?**
A: Admins can see user online status in real-time. Other users receive notifications when relevant team members are active.

### Technical Questions

**Q: Why are my notifications not working?**
A: Check your notification settings in your profile, ensure browser notifications are allowed, and verify your email settings. Contact support if issues persist.

**Q: The system is running slowly. What should I do?**
A: Try refreshing the page, clearing your browser cache, checking your internet connection, or using a different browser. Contact support for persistent performance issues.

**Q: Can I work offline?**
A: No, the system requires an internet connection. Save your work frequently and ensure stable connectivity.

**Q: What should I do if I encounter an error?**
A: Note the error message, try refreshing the page, and contact your administrator with details about what you were doing when the error occurred.

### Data and Security Questions

**Q: Is my data secure?**
A: Yes, the system uses industry-standard security measures including encrypted connections, secure authentication, and role-based access controls.

**Q: Who can see my data?**
A: Access is role-based. You can only see data relevant to your role and assigned projects. Admins have broader access for system management.

**Q: How long is data retained?**
A: Project data is retained indefinitely for historical reference. Activity logs are cleaned up periodically by administrators.

**Q: Can I export my data?**
A: Yes, various export options are available depending on your role, including PDF reports, Excel exports, and data downloads.

---

## Troubleshooting

### Common Issues and Solutions

#### Login and Access Problems

**Issue: Cannot log in with correct credentials**
- **Solution 1**: Verify email and password (check for typos and caps lock)
- **Solution 2**: Clear browser cache and cookies
- **Solution 3**: Try a different browser or incognito/private mode
- **Solution 4**: Check if account is locked (wait 15 minutes or contact admin)
- **Solution 5**: Ensure you're using the correct system URL

**Issue: Redirected to wrong dashboard**
- **Solution**: Contact administrator to verify your role assignment
- **Cause**: Usually indicates incorrect role configuration

**Issue: Session expires frequently**
- **Solution 1**: Check if you're using "Remember Me" option
- **Solution 2**: Ensure browser accepts cookies
- **Solution 3**: Avoid multiple browser tabs with the system open
- **Note**: Sessions expire after 30 days of inactivity for security

#### Dashboard and Performance Issues

**Issue: Dashboard loading slowly or not at all**
- **Solution 1**: Refresh the page (Ctrl+F5 or Cmd+Shift+R)
- **Solution 2**: Check internet connection speed and stability
- **Solution 3**: Clear browser cache and temporary files
- **Solution 4**: Disable browser extensions temporarily
- **Solution 5**: Try a different browser or device

**Issue: Charts and graphs not displaying**
- **Solution 1**: Ensure JavaScript is enabled in browser
- **Solution 2**: Update browser to latest version
- **Solution 3**: Check if ad blockers are interfering
- **Solution 4**: Try disabling browser extensions

**Issue: Data not updating in real-time**
- **Solution 1**: Refresh the page manually
- **Solution 2**: Check network connection stability
- **Solution 3**: Log out and log back in
- **Solution 4**: Contact administrator if data appears outdated

#### File Upload and Report Issues

**Issue: Cannot upload files or attachments**
- **Solution 1**: Check file size (maximum 10MB per file)
- **Solution 2**: Verify file format is supported (PDF, DOC, DOCX, XLS, XLSX, JPG, PNG)
- **Solution 3**: Ensure stable internet connection
- **Solution 4**: Try uploading one file at a time
- **Solution 5**: Check available storage space on device

**Issue: Report submission fails**
- **Solution 1**: Ensure all required fields are completed
- **Solution 2**: Check file attachments meet requirements
- **Solution 3**: Verify internet connection is stable
- **Solution 4**: Try submitting without attachments first, then add files
- **Solution 5**: Contact Project Manager if submission continues to fail

**Issue: PDF exports not working**
- **Solution 1**: Ensure pop-ups are allowed in browser
- **Solution 2**: Check if download folder has sufficient space
- **Solution 3**: Try a different browser
- **Solution 4**: Disable browser extensions temporarily

#### Notification Problems

**Issue: Not receiving notifications**
- **Solution 1**: Check notification settings in your profile
- **Solution 2**: Allow notifications in browser settings
- **Solution 3**: Check email spam/junk folder
- **Solution 4**: Verify email address is correct in profile
- **Solution 5**: Contact administrator to check notification system

**Issue: Too many notifications**
- **Solution**: Adjust notification preferences in user settings
- **Options**: Disable specific notification types while keeping important ones

#### Mobile Device Issues

**Issue: System not working properly on mobile**
- **Solution 1**: Use supported mobile browsers (Chrome, Safari)
- **Solution 2**: Ensure mobile browser is updated
- **Solution 3**: Try landscape orientation for better viewing
- **Solution 4**: Use desktop/laptop for complex tasks
- **Note**: Some features are optimized for desktop use

### Error Messages and Solutions

#### Common Error Messages

**"Access Denied" or "403 Forbidden"**
- **Cause**: Insufficient permissions for requested action
- **Solution**: Contact administrator to verify role permissions
- **Prevention**: Only access features appropriate for your role

**"Session Expired"**
- **Cause**: Inactive for extended period or security timeout
- **Solution**: Log in again with your credentials
- **Prevention**: Save work frequently and use "Remember Me" option

**"Network Error" or "Connection Failed"**
- **Cause**: Internet connectivity issues
- **Solution**: Check internet connection and try again
- **Prevention**: Ensure stable internet before important tasks

**"File Too Large"**
- **Cause**: Attempting to upload file larger than 10MB
- **Solution**: Compress file or use different format
- **Prevention**: Check file sizes before uploading

### Getting Help and Support

#### Internal Support Channels
1. **System Administrator**: Technical issues, account problems, system errors
2. **Project Managers**: Project-related questions, activity assignments
3. **Senior Managers**: Strategic questions, project approval issues
4. **Accountants**: Budget and financial questions

#### Self-Help Resources
- **This User Manual**: Comprehensive system documentation
- **In-System Help**: Tooltips and help text throughout the interface
- **FAQ Section**: Common questions and answers
- **Video Tutorials**: Available from administrator (if provided)

#### When to Contact Support
- **Immediate**: System errors, security concerns, data loss
- **Same Day**: Login problems, urgent project issues
- **Within 24 Hours**: Feature questions, training needs
- **Scheduled**: Training requests, system enhancement suggestions

#### Information to Provide When Seeking Help
- Your name and role
- Specific error messages (screenshot if possible)
- What you were trying to do when the problem occurred
- Browser and operating system information
- Steps you've already tried to resolve the issue

### Best Practices for Smooth Operation

#### Daily Practices
- Log out properly when finished
- Save work frequently
- Check notifications regularly
- Keep browser updated
- Use strong, unique passwords

#### Weekly Practices
- Clear browser cache
- Update any pending reports
- Review notification settings
- Check for system announcements

#### Monthly Practices
- Update password if required
- Review and update profile information
- Clean up local file downloads
- Provide feedback on system improvements

---

## Version Information

### Current System Version
**SPRODETA M&E System Version:** 2.0
**Release Date:** January 2025
**Manual Version:** 1.0
**Last Manual Update:** January 2025

### Version History

#### Version 2.0 (January 2025) - Current
**Major Features Added:**
- Enhanced M&E evaluation framework with KPI tracking
- Real-time budget calculation from approved reports
- Project-based team management system
- Comprehensive notification system with email integration
- Advanced reporting with PDF export capabilities
- Improved dashboard analytics with interactive charts
- Mobile-responsive design improvements

**Technical Improvements:**
- Upgraded to Next.js frontend framework
- Enhanced security with JWT authentication
- Improved database performance and indexing
- Real-time user status tracking
- Automated backup system implementation

**User Interface Enhancements:**
- Modern, professional design with baby blue theme
- Improved navigation with collapsible sidebar
- Enhanced form validation and user feedback
- Better mobile device compatibility
- Accessibility improvements

#### Version 1.5 (Previous Release)
**Features:**
- Basic project management functionality
- Simple activity assignment system
- Basic reporting capabilities
- User role management
- Email notifications

#### Version 1.0 (Initial Release)
**Features:**
- Core project creation and management
- User authentication system
- Basic dashboard functionality
- Simple reporting system

### Upcoming Features (Roadmap)

#### Version 2.1 (Planned - Q2 2025)
- **Enhanced Analytics**: Advanced data visualization and trend analysis
- **Mobile App**: Native mobile application for field officers
- **Offline Capability**: Limited offline functionality for remote areas
- **Integration APIs**: Third-party system integration capabilities
- **Advanced Workflows**: Customizable approval workflows

#### Version 2.2 (Planned - Q3 2025)
- **AI-Powered Insights**: Machine learning for project predictions
- **Advanced Scheduling**: Gantt chart improvements and resource planning
- **Multi-language Support**: Localization for multiple languages
- **Enhanced Security**: Two-factor authentication and advanced security features

### System Dependencies

#### Frontend Technologies
- **Next.js**: 14.0+ (React framework)
- **React**: 18.0+ (User interface library)
- **TypeScript**: 5.0+ (Programming language)
- **Tailwind CSS**: 3.0+ (Styling framework)

#### Backend Technologies
- **Node.js**: 18.0+ (Runtime environment)
- **Express.js**: 4.18+ (Web framework)
- **MongoDB**: 6.0+ (Database)
- **Mongoose**: 7.0+ (Database ODM)

#### Security and Authentication
- **JWT**: JSON Web Tokens for authentication
- **bcrypt**: Password hashing
- **CORS**: Cross-origin resource sharing
- **Helmet**: Security headers

### Browser Compatibility Matrix

| Browser | Minimum Version | Recommended Version | Notes |
|---------|----------------|-------------------|-------|
| Chrome | 90 | Latest | Best performance |
| Firefox | 88 | Latest | Full compatibility |
| Edge | 90 | Latest | Windows recommended |
| Safari | 14 | Latest | macOS/iOS only |
| Mobile Chrome | 90 | Latest | Android devices |
| Mobile Safari | 14 | Latest | iOS devices |

### Known Issues and Limitations

#### Current Known Issues
- **Large File Uploads**: Files over 8MB may timeout on slow connections
- **Mobile Charts**: Some complex charts may not display optimally on small screens
- **PDF Generation**: Large reports (>50 pages) may take extended time to generate
- **Real-time Updates**: Notifications may have 30-60 second delay

#### System Limitations
- **Maximum Users**: 1000 concurrent users
- **File Storage**: 10GB total system storage for uploads
- **Report History**: 2 years of detailed activity logs retained
- **Session Timeout**: 30 days maximum session duration

### Support and Maintenance

#### Regular Maintenance Schedule
- **Daily**: Automated backups at 2:00 AM local time
- **Weekly**: System performance monitoring and optimization
- **Monthly**: Security updates and patch deployment
- **Quarterly**: Major feature updates and system upgrades

#### Emergency Maintenance
- **Notification**: 24-hour advance notice when possible
- **Duration**: Typically 1-4 hours for major updates
- **Backup Access**: Read-only access during maintenance when possible

#### End-of-Life Policy
- **Version Support**: Each major version supported for 2 years
- **Security Updates**: Critical security patches for 3 years
- **Migration Assistance**: 6-month overlap period for version transitions

---

*This manual covers the complete functionality of the SPRODETA M&E system version 2.0. For additional features, updates, or specific questions not covered in this manual, please contact your system administrator or refer to the latest system documentation.*

**Document Information:**
- **Created**: January 2025
- **Authors**: SPRODETA Development Team
- **Review Cycle**: Quarterly updates
- **Next Review**: April 2025
